import React, {useState, useLayoutEffect, useEffect} from 'react';
import {
  StyleSheet,
  FlatList,
  SafeAreaView,
  View,
  ActivityIndicator,
  TouchableOpacity,
} from 'react-native';
import {Text} from 'react-native-paper';
import axios from 'axios';
import CustomCategoriesDetailCard from '../../components/CustomCategoriesDetailCard';
import CustomSearchBar from '../../components/CustomSearchBar';
import ShortcutButton from '../../components/ShortcutButton';
import {CONFIG} from '../../common/constant';
import commonStyles from '../../common/commonStyles';
import {showErrorToast} from '../../utils/showToast.js';
// Add these imports
import watermelonCompanyRepository from '../../database/watermelon/repositories/companyRepository';
import {useAppShortcuts} from '../../hooks/useAppShortcuts';
import watermelonCompanyCategoryRepository from '../../database/watermelon/repositories/companyCategoryRepository';
import syncStateRepository from '../../database/watermelon/repositories/syncStateRepository';
import {UICompany} from '../../types/company';
import {
  convertDBCompaniesToUI,
  convertApiCompaniesToUI,
} from '../../utils/companyConverter';

const CompanyScreen = ({route, navigation}: {route: any; navigation: any}) => {
  // Add state for sync status
  const [isFullySynced, setIsFullySynced] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [page, setPage] = useState(1);
  const [hasMoreData, setHasMoreData] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [isSearching, setIsSearching] = useState(false);
  // Add state to track if current category is Bank category and holiday message
  const [isBankCategory, setIsBankCategory] = useState(false);
  const [bankHolidayMessage, setBankHolidayMessage] = useState('');

  // Initialize app shortcuts hook
  const {
    isShortcutSupported,
    createCompanyListShortcut,
    isLoading: shortcutLoading,
  } = useAppShortcuts(navigation);

  const {title, categoryId = 50} = route.params;
  const [loading, setLoading] = useState(false); // Start with false to show UI immediately
  const [companies, setCompanies] = useState<UICompany[]>([]);

  // Utility functions for bank holiday detection
  const getNthSaturdayOfMonth = (
    year: number,
    month: number,
    n: number,
  ): Date => {
    const firstDay = new Date(year, month, 1);
    const firstSaturday = new Date(firstDay);

    // Find first Saturday of the month
    const daysToSaturday = (6 - firstDay.getDay()) % 7;
    firstSaturday.setDate(1 + daysToSaturday);

    // Calculate nth Saturday
    const nthSaturday = new Date(firstSaturday);
    nthSaturday.setDate(firstSaturday.getDate() + (n - 1) * 7);

    return nthSaturday;
  };

  const isNationalHoliday = (date: Date): string | null => {
    const day = date.getDate();
    const month = date.getMonth() + 1; // getMonth() returns 0-11

    if (month === 1 && day === 26) return 'Republic Day';
    if (month === 8 && day === 15) return 'Independence Day';
    if (month === 10 && day === 2) return 'Gandhi Jayanti';

    return null;
  };

  const isBankHoliday = (date: Date): {isHoliday: boolean; message: string} => {
    const year = date.getFullYear();
    const month = date.getMonth();
    const day = date.getDate();

    // Check for national holidays
    const nationalHoliday = isNationalHoliday(date);
    if (nationalHoliday) {
      return {
        isHoliday: true,
        message: `Banks are closed today for ${nationalHoliday}`,
      };
    }

    // Check for 2nd and 4th Saturday
    const secondSaturday = getNthSaturdayOfMonth(year, month, 2);
    const fourthSaturday = getNthSaturdayOfMonth(year, month, 4);

    if (day === secondSaturday.getDate() && date.getDay() === 6) {
      return {
        isHoliday: true,
        message: 'Banks are closed today (2nd Saturday of the month)',
      };
    }

    if (day === fourthSaturday.getDate() && date.getDay() === 6) {
      return {
        isHoliday: true,
        message: 'Banks are closed today (4th Saturday of the month)',
      };
    }

    return {isHoliday: false, message: ''};
  };

  // Check if current category is Bank category and if today is a bank holiday
  const checkBankHolidayStatus = () => {
    // Check if the categoryId from route params is 2 (Bank category)
    const isBankCat = categoryId === 2;

    if (!isBankCat) {
      setIsBankCategory(false);
      setBankHolidayMessage('');
      console.log(
        `[CompanyScreen] Category ${categoryId} is not Bank category`,
      );
      return;
    }

    // Check if today is a bank holiday
    const today = new Date();
    const holidayStatus = isBankHoliday(today);

    setIsBankCategory(holidayStatus.isHoliday);
    setBankHolidayMessage(holidayStatus.message);

    console.log(
      `[CompanyScreen] Category ${categoryId} is Bank category. Holiday status: ${holidayStatus.isHoliday}`,
    );

    if (holidayStatus.isHoliday) {
      console.log(
        `[CompanyScreen] Bank holiday message: ${holidayStatus.message}`,
      );
    }
  };

  // Check if data is fully synced (optimized with timing)
  const checkSyncStatus = async () => {
    try {
      const syncCheckStartTime = Date.now();

      // Get sync states for companies and company_categories in parallel
      const [companySyncState, companyCategorySyncState] = await Promise.all([
        syncStateRepository.getBySyncKey('companies'),
        syncStateRepository.getBySyncKey('company_categories'),
      ]);

      // Check if both are fully synced (progress = 100)
      const isCompanySynced =
        companySyncState && companySyncState.progress === 100;
      const isCompanyCategorySynced =
        companyCategorySyncState && companyCategorySyncState.progress === 100;

      // Both must be synced to use local data
      const fullySync = !!(isCompanySynced && isCompanyCategorySynced);
      setIsFullySynced(fullySync);

      const syncCheckEndTime = Date.now();
      console.log(
        `[CompanyScreen] Sync status check completed in ${
          syncCheckEndTime - syncCheckStartTime
        }ms - Companies: ${isCompanySynced} (${companySyncState?.status}, ${
          companySyncState?.progress
        }%), Categories: ${isCompanyCategorySynced} (${
          companyCategorySyncState?.status
        }, ${companyCategorySyncState?.progress}%)`,
      );

      return fullySync;
    } catch (error) {
      console.error('[CompanyScreen] Error checking sync status:', error);
      return false;
    }
  };

  // Fetch companies from local database (includes local search functionality)
  const fetchCompaniesFromLocalDB = async (searchQuery = '') => {
    try {
      if (searchQuery) {
        // Show search loading indicator for search operations
        setIsSearching(true);
      } else {
        // Show main loading indicator for initial loads
        setLoading(true);
      }
      const startTime = Date.now();
      console.log(
        `[CompanyScreen] Fetching companies for category ${categoryId} from local database`,
      );

      // 1. Get company-category relationships for this category
      const relationshipStartTime = Date.now();
      const companyCategories =
        await watermelonCompanyCategoryRepository.getByCategoryId(categoryId);
      const relationshipEndTime = Date.now();
      console.log(
        `[CompanyScreen] Found ${
          companyCategories.length
        } company-category relationships in ${
          relationshipEndTime - relationshipStartTime
        }ms`,
      );

      if (companyCategories.length === 0) {
        setCompanies([]);
        setHasMoreData(false);
        return;
      }

      // 2. Extract company IDs
      const companyIds = companyCategories.map(cc => cc.companyId);

      // 3. Get companies by their IDs using batch query (much faster than individual queries)
      const companiesStartTime = Date.now();
      const localCompanies = await watermelonCompanyRepository.getByCompanyIds(
        companyIds,
      );
      const companiesEndTime = Date.now();
      console.log(
        `[CompanyScreen] Fetched ${localCompanies.length} companies in ${
          companiesEndTime - companiesStartTime
        }ms`,
      );

      // 4. Apply search filter locally (no API call needed)
      let filteredCompanies = localCompanies;
      if (searchQuery) {
        console.log(
          `[CompanyScreen] Applying local search filter for: "${searchQuery}"`,
        );
        filteredCompanies = localCompanies.filter(company =>
          company.company_name
            .toLowerCase()
            .includes(searchQuery.toLowerCase()),
        );
        console.log(
          `[CompanyScreen] Local search found ${filteredCompanies.length} matching companies`,
        );
      }

      // 5. Convert to the format expected by the UI using the converter utility
      const formattedCompanies = convertDBCompaniesToUI(filteredCompanies);

      const endTime = Date.now();
      console.log(
        `[CompanyScreen] ✅ Loaded ${
          formattedCompanies.length
        } companies from local database in ${endTime - startTime}ms`,
      );

      // Debug: Check number fields in the first few companies
      formattedCompanies.slice(0, 3).forEach((company, index) => {
        console.log(
          `[CompanyScreen] Company ${index + 1}: ${
            company.companyName
          } - Number: "${company.number}"`,
        );
      });

      setCompanies(formattedCompanies);
      setHasMoreData(false); // No pagination for local data
      setError(null);
    } catch (err: any) {
      console.error('[CompanyScreen] Error fetching from local DB:', err);
      setError('Failed to load companies from local database');
      // Fall back to API if local fetch fails
      fetchCompaniesFromAPI(1, searchQuery, true);
    } finally {
      setLoading(false);
      setRefreshing(false);
      setIsSearching(false);
    }
  };

  const buildApiUrl = (pageNum: number, searchQuery: string) => {
    return `${
      CONFIG.API_URL
    }/company?categoryId=${categoryId}&page=${pageNum}&limit=20&sortBy=created_at&sortOrder=DESC${
      searchQuery ? `&search=${searchQuery}` : ''
    }`;
  };

  // Rename to fetchCompaniesFromAPI for clarity
  const fetchCompaniesFromAPI = async (
    pageNum = 1,
    searchQuery = '',
    shouldReset = false,
  ) => {
    try {
      if (shouldReset && searchQuery) {
        // Only show search loading indicator for actual search operations
        setIsSearching(true);
      } else {
        // Show main loading indicator for initial loads and pagination
        setLoading(true);
      }
      const apiUrl = buildApiUrl(pageNum, searchQuery);

      console.info(`
        ====================================
        ============== Request =============
        URL: ${apiUrl}
        Method: GET
        Headers: ${JSON.stringify(
          {
            Accept: 'application/json',
            'Content-Type': 'application/json',
          },
          null,
          2,
        )}
        ====================================
      `);

      const response = await axios.get(apiUrl, {
        headers: {
          Accept: 'application/json',
          'Content-Type': 'application/json',
          // Add authorization header if needed
          // 'Authorization': 'Bearer YOUR_TOKEN'
        },
        timeout: 50000, // 50 seconds timeout
      });

      console.info(`
        ====================================
        ============= Response =============
        Status: ${response.status}
        Data: ${JSON.stringify(response.data, null, 2)}
        ====================================
      `);

      // Check if we received data and it was successful
      if (
        response.data &&
        response.data.success &&
        response.data.data &&
        response.data.data.companies
      ) {
        // Convert API companies to UI format before using them
        const apiCompanies = response.data.data.companies;
        const receivedCompanies = convertApiCompaniesToUI(apiCompanies);

        // If shouldReset is true, replace the companies array
        // Otherwise append to existing companies (for pagination)
        const newCompanies = shouldReset
          ? receivedCompanies
          : [...companies, ...receivedCompanies];
        setCompanies(newCompanies);

        // Check if we've reached the end of the data
        // We check if the length of companies received is less than the limit (20)
        // or if we've reached the total number of companies
        const totalCompanies = response.data.data.total || 0;
        const hasMoreToLoad =
          receivedCompanies.length === 20 &&
          (shouldReset
            ? receivedCompanies.length
            : companies.length + receivedCompanies.length) < totalCompanies;
        setHasMoreData(hasMoreToLoad);

        // Clear any previous errors
        setError(null);
      } else {
        setHasMoreData(false);
      }
    } catch (err: any) {
      console.error('API Error:', err);

      // Handle Axios errors
      if (err.response) {
        // Server responded with error status
        console.error('Error response data:', err.response.data);
        console.error('Error response status:', err.response.status);
        const errorMessage =
          err.response.data?.message || `Server error: ${err.response.status}`;
        showErrorToast(new Error(errorMessage));
        setError(errorMessage);
      } else if (err.request) {
        // Request was made but no response received
        console.error('No response received:', err.request);
        const errorMessage = 'Network error: No response from server';
        showErrorToast(new Error(errorMessage));
        setError(errorMessage);
      } else {
        // Something else happened
        console.error('Error message:', err.message);
        showErrorToast(err);
        setError(err.message || 'Failed to fetch companies');
      }
    } finally {
      setLoading(false);
      setRefreshing(false);
      setIsSearching(false);
    }
  };

  // Main fetch function that decides whether to use local or API data
  const fetchCompanies = async (
    pageNum = 1,
    searchQuery = '',
    shouldReset = false,
  ) => {
    // Check sync status first
    const isSynced = await checkSyncStatus();

    // If data is fully synced, use local database for both regular fetching and searching
    if (isSynced) {
      console.log(
        '[CompanyScreen] Using local data source (including for search)',
      );
      fetchCompaniesFromLocalDB(searchQuery);
      return;
    }

    // If not fully synced, check if sync is currently in progress
    const [companySyncState, companyCategorySyncState] = await Promise.all([
      syncStateRepository.getBySyncKey('companies'),
      syncStateRepository.getBySyncKey('company_categories'),
    ]);

    const isCompanySyncInProgress = companySyncState?.status === 'in_progress';
    const isCompanyCategorySyncInProgress =
      companyCategorySyncState?.status === 'in_progress';

    // If sync is in progress, avoid using local data as it might be inconsistent
    if (isCompanySyncInProgress || isCompanyCategorySyncInProgress) {
      console.log(
        `[CompanyScreen] Sync in progress (Companies: ${companySyncState?.status}, Categories: ${companyCategorySyncState?.status}). Using API data to avoid inconsistency.`,
      );
      fetchCompaniesFromAPI(pageNum, searchQuery, shouldReset);
      return;
    }

    // If sync is not in progress, check if we have stable local data
    const companyCount = await watermelonCompanyRepository.getCount();
    const companyCategoryCount =
      await watermelonCompanyCategoryRepository.getCount();

    if (companyCount > 0 && companyCategoryCount > 0) {
      console.log(
        `[CompanyScreen] Sync not in progress and we have stable data (${companyCount} companies, ${companyCategoryCount} relationships). Using local data.`,
      );
      try {
        await fetchCompaniesFromLocalDB(searchQuery);
        return; // If local fetch succeeds, we're done
      } catch (error) {
        console.warn(
          '[CompanyScreen] Local data fetch failed, falling back to API:',
          error,
        );
      }
    }

    // Fall back to API if no local data or local fetch failed
    console.log(
      '[CompanyScreen] No stable local data available. Using API data source.',
    );
    fetchCompaniesFromAPI(pageNum, searchQuery, shouldReset);
  };

  // Handle search
  const handleSearch = (query: string) => {
    setPage(1);
    setSearchQuery(query);
    fetchCompanies(1, query, true);
  };

  // Initial data fetch on component mount
  useEffect(() => {
    // Show UI immediately, then start loading data
    const initializeScreen = async () => {
      checkBankHolidayStatus(); // Check if this is Bank category and if today is a bank holiday

      // Small delay to ensure UI renders first
      setTimeout(() => {
        fetchCompanies(1, '', true);
      }, 50);
    };

    initializeScreen();
  }, [categoryId]);

  // Listen for sync completion to automatically switch to local data
  useEffect(() => {
    let syncCheckInterval: NodeJS.Timeout;

    const checkForSyncCompletion = async () => {
      if (isFullySynced) {
        // Already using local data, no need to check
        return;
      }

      const isSynced = await checkSyncStatus();
      if (isSynced && !isFullySynced) {
        console.log(
          '[CompanyScreen] Sync completed! Switching to local data...',
        );
        // Refresh data to use local database
        fetchCompanies(1, searchQuery, true);
      }
    };

    // Check every 3 seconds for sync completion
    syncCheckInterval = setInterval(checkForSyncCompletion, 3000);

    // Cleanup interval on unmount
    return () => {
      if (syncCheckInterval) {
        clearInterval(syncCheckInterval);
      }
    };
  }, [isFullySynced, searchQuery]);

  // Load more data when reaching the end of the list
  const loadMoreCompanies = () => {
    // Only load more if using API data (not local)
    // Local data loads all results at once, so no pagination needed
    if (!loading && hasMoreData && !isFullySynced) {
      const nextPage = page + 1;
      setPage(nextPage);
      fetchCompanies(nextPage, searchQuery);
    }
  };

  // Handle refresh
  const handleRefresh = () => {
    setRefreshing(true);
    setPage(1);
    fetchCompanies(1, searchQuery, true);
  };

  useLayoutEffect(() => {
    navigation.setOptions({title});
  }, [navigation, title]);

  // Render loading indicator
  const renderLoadingIndicator = () => {
    // Don't show main loading indicator if search loading is active or if refreshing
    if (!loading || refreshing || isSearching) return null;

    return (
      <View style={commonStyles.loadingContainer}>
        <ActivityIndicator size="large" color="#0000ff" />
        <Text style={commonStyles.loadingText}>Loading companies...</Text>
      </View>
    );
  };

  // Render empty list message
  const renderEmptyList = () => {
    // Show loading indicator only for main loading (not search loading)
    if (loading && !refreshing && !isSearching) return renderLoadingIndicator();

    return (
      <View style={commonStyles.emptyContainer}>
        <Text style={commonStyles.emptyText}>
          {searchQuery
            ? 'No companies match your search.'
            : 'No companies found in this category.'}
        </Text>
      </View>
    );
  };

  // Use UICompany type for consistency
  type Company = UICompany;

  // Render a single company item
  const renderCompanyItem = ({item}: {item: Company}) => (
    <CustomCategoriesDetailCard companyData={item} />
  );

  return (
    <SafeAreaView style={styles.container}>
      {/* Show Bank Holidays label only for Bank category (categoryId = 2) and on specific holidays */}
      {isBankCategory && bankHolidayMessage && (
        <View style={{backgroundColor: '#F89696'}}>
          <Text style={commonStyles.labelTopInstructor}>
            {bankHolidayMessage}
          </Text>
        </View>
      )}
      {/* Temporarily hidden complaint information
      <View style={{paddingLeft: 15, paddingRight: 15}}>
        <Text
          style={[
            commonStyles.instructionText,
            commonStyles.instructionTextTopSpace,
          ]}>
          For Cyber Fraud Complaint: 1930
        </Text>
        <Text
          style={[
            commonStyles.instructionText,
            commonStyles.instructionTextBottomSpace,
          ]}>
          For Complaint Through RBI: 14440
        </Text>
      </View>
      */}
      <View style={{marginTop: 10}} />
      <CustomSearchBar
        onSearch={handleSearch}
        isSearching={isSearching}
        initialValue={searchQuery}
        placeholder="Search companies"
      />

      {/* Add to Home Screen shortcut button */}
      {isShortcutSupported && (
        <View style={{paddingHorizontal: 15, paddingTop: 10}}>
          <ShortcutButton
            title={`Add "${title}" to Home Screen`}
            onPress={() => createCompanyListShortcut(categoryId, title)}
            isLoading={shortcutLoading}
          />
        </View>
      )}

      <View style={{flex: 1}}>
        {/* Error display */}
        {error && (
          <View style={commonStyles.errorContainer}>
            <Text style={commonStyles.errorText}>{error}</Text>
            <TouchableOpacity
              style={styles.retryButton}
              onPress={() => fetchCompanies(1, searchQuery, true)}>
              <Text style={styles.retryButtonText}>Retry</Text>
            </TouchableOpacity>
          </View>
        )}
        <FlatList
          style={{padding: 15}}
          data={companies}
          extraData={companies}
          keyExtractor={item => item.companyId.toString()}
          renderItem={renderCompanyItem}
          showsVerticalScrollIndicator={false}
          showsHorizontalScrollIndicator={false}
          onEndReached={loadMoreCompanies}
          onEndReachedThreshold={0.3}
          ListEmptyComponent={renderEmptyList}
          refreshing={refreshing}
          onRefresh={handleRefresh}
          contentContainerStyle={
            companies.length === 0 ? commonStyles.fullHeight : null
          }
        />
      </View>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#fff',
  },
  retryButton: {
    backgroundColor: '#ef5350',
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 4,
  },
  retryButtonText: {
    color: 'white',
    fontWeight: '600',
  },
});

export default CompanyScreen;
